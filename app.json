{"pages": ["pages/home/<USER>", "pages/login/login", "pages/health/health", "pages/health/record-list/record-list", "pages/health/record-detail", "pages/health/record-detail/record-detail", "pages/health/knowledge/knowledge", "pages/health/knowledge/detail/detail", "pages/health/knowledge-detail/knowledge-detail", "pages/health/report/report", "pages/health/ai-diagnosis/ai-diagnosis", "pages/production/production", "pages/production/environment/environment", "pages/production/materials/materials", "pages/production/materials/detail/detail", "pages/production/materials/inventory/inventory", "pages/production/finance/finance", "pages/production/records/records", "pages/production/record-detail", "pages/production/reimbursement/reimbursement", "pages/production/reimbursement/add/add", "pages/production/reimbursement/detail/detail", "pages/production/ai-inventory/ai-inventory", "pages/ai-config/ai-config", "pages/shop/shop", "pages/shop/goods-detail", "pages/shop/goods/add/add", "pages/shop/cart", "pages/shop/checkout", "pages/shop/order-success", "pages/orders/orders", "pages/order-detail/order-detail", "pages/payment/payment", "pages/logistics/logistics", "pages/address/address", "pages/profile/profile", "pages/profile/settings/settings", "pages/profile/help/help", "pages/more/more", "pages/announcement/announcement-list/announcement-list", "pages/announcement/announcement-detail/announcement-detail", "pages/price/price-detail/price-detail", "pages/inventory/inventory-detail/inventory-detail", "pages/task/task-list/task-list", "pages/task/detail/detail", "pages/weather/weather-detail/weather-detail", "pages/oa/oa", "pages/oa/leave/leave", "pages/oa/leave/list/list", "pages/oa/leave/detail/detail", "pages/oa/leave/apply/apply", "pages/oa/purchase/purchase", "pages/oa/purchase/list/list", "pages/oa/purchase/detail/detail", "pages/oa/purchase/apply/apply", "pages/oa/reimbursement/reimbursement", "pages/oa/reimbursement/list/list", "pages/oa/reimbursement/detail/detail", "pages/oa/reimbursement/apply/apply", "pages/oa/finance/finance", "pages/oa/finance/overview/overview", "pages/oa/finance/reports/reports", "pages/oa/approval/approval", "pages/oa/approval/pending/pending", "pages/oa/approval/history/history", "pages/oa/workflow/workflow", "pages/oa/workflow/templates/templates", "pages/oa/workflow/instances/instances", "pages/oa/notification/notification", "pages/oa/permission/users/users", "pages/oa/permission/roles/roles", "pages/oa/workflow/designer/designer"], "window": {"backgroundTextStyle": "light", "navigationBarBackgroundColor": "#0066CC", "navigationBarTitleText": "智慧养鹅", "navigationBarTextStyle": "white", "backgroundColor": "#f5f5f5"}, "tabBar": {"custom": false, "color": "#7A7E83", "selectedColor": "#0066CC", "backgroundColor": "#ffffff", "borderStyle": "black", "list": [{"pagePath": "pages/home/<USER>", "text": "首页"}, {"pagePath": "pages/health/health", "text": "健康"}, {"pagePath": "pages/production/production", "text": "生产"}, {"pagePath": "pages/shop/shop", "text": "商城"}, {"pagePath": "pages/profile/profile", "text": "我的"}]}, "networkTimeout": {"request": 10000, "downloadFile": 10000}, "debug": false, "permission": {"scope.userLocation": {"desc": "你的位置信息将用于小程序位置接口的效果展示"}}, "requiredBackgroundModes": ["audio"], "plugins": {}, "resizable": true, "sitemapLocation": "sitemap.json", "usingComponents": {"global-icon": "/components/global/icon/icon", "global-page-header": "/components/global/page-header/page-header"}}