{"pages": ["pages/home/<USER>", "pages/login/login", "pages/profile/profile"], "subPackages": [{"root": "packages/health", "name": "health", "pages": ["health/health", "record-list/record-list", "record-detail/record-detail", "knowledge/knowledge", "knowledge/detail/detail", "knowledge-detail/knowledge-detail", "report/report", "ai-diagnosis/ai-diagnosis"]}, {"root": "packages/production", "name": "production", "pages": ["production/production", "environment/environment", "materials/materials", "materials/detail/detail", "materials/inventory/inventory", "finance/finance", "records/records", "record-detail", "reimbursement/reimbursement", "reimbursement/add/add", "reimbursement/detail/detail", "ai-inventory/ai-inventory"]}, {"root": "packages/shop", "name": "shop", "pages": ["shop/shop", "goods-detail", "goods/add/add", "cart", "checkout", "order-success"]}, {"root": "packages/oa", "name": "oa", "pages": ["oa/oa", "leave/leave", "leave/list/list", "leave/detail/detail", "leave/apply/apply", "purchase/purchase", "purchase/list/list", "purchase/detail/detail", "purchase/apply/apply", "reimbursement/reimbursement", "reimbursement/list/list", "reimbursement/detail/detail", "reimbursement/apply/apply", "finance/finance", "finance/overview/overview", "finance/reports/reports", "approval/approval", "approval/pending/pending", "approval/history/history", "workflow/workflow", "workflow/templates/templates", "workflow/instances/instances", "notification/notification", "permission/users/users", "permission/roles/roles", "workflow/designer/designer"]}, {"root": "packages/common", "name": "common", "pages": ["ai-config/ai-config", "orders/orders", "order-detail/order-detail", "payment/payment", "logistics/logistics", "address/address", "profile/settings/settings", "profile/help/help", "more/more", "announcement/announcement-list/announcement-list", "announcement/announcement-detail/announcement-detail", "price/price-detail/price-detail", "inventory/inventory-detail/inventory-detail", "task/task-list/task-list", "task/detail/detail", "weather/weather-detail/weather-detail"]}], "preloadRule": {"packages/health": {"network": "all", "packages": ["health"]}, "packages/production": {"network": "all", "packages": ["production"]}}, "window": {"backgroundTextStyle": "light", "navigationBarBackgroundColor": "#0066CC", "navigationBarTitleText": "智慧养鹅", "navigationBarTextStyle": "white", "backgroundColor": "#f5f5f5"}, "tabBar": {"custom": false, "color": "#7A7E83", "selectedColor": "#0066CC", "backgroundColor": "#ffffff", "borderStyle": "black", "list": [{"pagePath": "pages/home/<USER>", "text": "首页"}, {"pagePath": "pages/health/health", "text": "健康"}, {"pagePath": "pages/production/production", "text": "生产"}, {"pagePath": "pages/shop/shop", "text": "商城"}, {"pagePath": "pages/profile/profile", "text": "我的"}]}, "networkTimeout": {"request": 10000, "downloadFile": 10000}, "debug": false, "permission": {"scope.userLocation": {"desc": "你的位置信息将用于小程序位置接口的效果展示"}}, "requiredBackgroundModes": ["audio"], "plugins": {}, "resizable": true, "sitemapLocation": "sitemap.json", "usingComponents": {"global-icon": "/components/global/icon/icon", "global-page-header": "/components/global/page-header/page-header", "lazy-image": "/components/lazy-image/lazy-image"}}