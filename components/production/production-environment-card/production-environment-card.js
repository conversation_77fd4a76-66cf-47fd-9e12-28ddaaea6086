// components/production/production-environment-card/production-environment-card.js

/**
 * Production环境监控卡片组件
 * 功能：实时环境数据展示、状态监控、趋势分析、智能预警
 * 设计：工业级环境监控界面，支持多种数据类型和显示模式
 */

Component({
  options: {
    addGlobalClass: true,
    multipleSlots: true
  },

  properties: {
    // 环境数据类型
    type: {
      type: String,
      value: 'temperature', // temperature, humidity, pm25, light, co2, nh3
    },

    // 当前数值
    value: {
      type: Number,
      value: 0,
      observer: function(newVal, oldVal) {
        this.checkThreshold(newVal);
        this.updateDisplayValue(newVal);
      }
    },

    // 数据单位
    unit: {
      type: String,
      value: ''
    },

    // 当前状态
    status: {
      type: String,
      value: 'normal', // normal, warning, danger, offline
      observer: function(newVal, oldVal) {
        if (newVal === null || newVal === undefined || newVal === 'null' || newVal === 'undefined') {
          console.warn('production-environment-card组件status属性接收到无效值:', newVal, '已重置为normal');
          this.setData({ status: 'normal' });
        }
      }
    },

    // 阈值配置
    threshold: {
      type: Object,
      value: {},
      observer: function(newVal) {
        if (newVal && this.data.value !== undefined) {
          this.checkThreshold(this.data.value);
        }
      }
    },

    // 趋势数据
    trend: {
      type: Array,
      value: []
    },

    // 是否显示趋势图
    showTrend: {
      type: Boolean,
      value: true
    },

    // 是否显示预警
    showAlert: {
      type: Boolean,
      value: true
    },

    // 显示模式
    mode: {
      type: String,
      value: 'card' // card, compact, dashboard
    },

    // 更新时间
    updateTime: {
      type: String,
      value: ''
    },

    // 自定义样式类
    customClass: {
      type: String,
      value: ''
    }
  },

  data: {
    // 处理后的显示值
    displayValue: '',
    
    // 环境类型配置
    typeConfig: {
      temperature: {
        name: '温度',
        icon: '温度',
        color: '#FF6B35',
        defaultThreshold: { min: 18, max: 28, optimal: [20, 26] },
        formatValue: (val) => val.toFixed(1)
      },
      humidity: {
        name: '湿度',
        icon: '湿度',
        color: '#4ECDC4',
        defaultThreshold: { min: 40, max: 80, optimal: [50, 70] },
        formatValue: (val) => val.toFixed(0)
      },
      pm25: {
        name: 'PM2.5',
        icon: '空气',
        color: '#45B7D1',
        defaultThreshold: { min: 0, max: 75, optimal: [0, 35] },
        formatValue: (val) => val.toFixed(0)
      },
      light: {
        name: '光照',
        icon: '光照',
        color: '#FFA726',
        defaultThreshold: { min: 200, max: 2000, optimal: [500, 1500] },
        formatValue: (val) => val.toFixed(0)
      },
      co2: {
        name: 'CO₂',
        icon: '气体',
        color: '#8E24AA',
        defaultThreshold: { min: 0, max: 1000, optimal: [0, 400] },
        formatValue: (val) => val.toFixed(0)
      },
      nh3: {
        name: 'NH₃',
        icon: '气体',
        color: '#D32F2F',
        defaultThreshold: { min: 0, max: 25, optimal: [0, 10] },
        formatValue: (val) => val.toFixed(1)
      }
    },

    // 状态配置
    statusConfig: {
      normal: {
        text: '正常',
        color: '#4CAF50',
        bgColor: '#E8F5E8',
        icon: '成功'
      },
      warning: {
        text: '警告',
        color: '#FF9800',
        bgColor: '#FFF3E0',
        icon: '警告'
      },
      danger: {
        text: '危险',
        color: '#F44336',
        bgColor: '#FFEBEE',
        icon: '错误'
      },
      offline: {
        text: '离线',
        color: '#9E9E9E',
        bgColor: '#F5F5F5',
        icon: '离线'
      }
    },

    // 趋势方向
    trendDirection: 'stable', // up, down, stable

    // 计算后的配置
    currentConfig: {},
    currentStatusConfig: {}
  },

  methods: {
    // 检查阈值状态
    checkThreshold: function(value) {
      const threshold = this.data.threshold || this.data.typeConfig[this.data.type]?.defaultThreshold;
      
      if (!threshold || value === undefined || value === null) {
        return;
      }

      let newStatus = 'normal';
      
      if (threshold.optimal) {
        // 有最优范围
        if (value < threshold.optimal[0] || value > threshold.optimal[1]) {
          newStatus = 'warning';
        }
        if (value < threshold.min || value > threshold.max) {
          newStatus = 'danger';
        }
      } else {
        // 只有最大最小值
        if (value < threshold.min || value > threshold.max) {
          newStatus = 'danger';
        }
      }

      // 只有状态变化时才更新
      if (newStatus !== this.data.status) {
        this.setData({
          status: newStatus
        });
        
        // 触发状态变化事件
        this.triggerEvent('statusChange', {
          type: this.data.type,
          value: value,
          status: newStatus,
          threshold: threshold
        });
      }
    },

    // 更新显示值
    updateDisplayValue: function(value) {
      const config = this.data.typeConfig[this.data.type];
      const displayValue = config?.formatValue ? config.formatValue(value) : value.toString();
      
      this.setData({
        displayValue: displayValue
      });
    },

    // 计算趋势方向
    calculateTrend: function() {
      const { trend } = this.data;
      if (trend.length < 2) {
        this.setData({ trendDirection: 'stable' });
        return;
      }

      const recent = trend.slice(-5); // 取最近5个数据点
      const firstValue = recent[0];
      const lastValue = recent[recent.length - 1];
      const threshold = Math.abs(firstValue * 0.05); // 5%的变化阈值

      let direction = 'stable';
      if (Math.abs(lastValue - firstValue) > threshold) {
        direction = lastValue > firstValue ? 'up' : 'down';
      }

      this.setData({
        trendDirection: direction
      });
    },

    // 获取趋势图表数据
    getTrendChartData: function() {
      const { trend } = this.data;
      if (trend.length === 0) return [];

      return trend.map((value, index) => ({
        x: index,
        y: value
      }));
    },

    // 卡片点击事件
    onCardTap: function() {
      if (this.data.mode === 'compact') {
        // 紧凑模式点击展开详情
        this.triggerEvent('expand', {
          type: this.data.type,
          value: this.data.value,
          status: this.data.status
        });
      } else {
        // 普通模式点击查看趋势
        this.onViewTrend();
      }
    },

    // 查看趋势
    onViewTrend: function() {
      this.triggerEvent('viewTrend', {
        type: this.data.type,
        value: this.data.value,
        trend: this.data.trend,
        config: this.data.currentConfig
      });
    },

    // 刷新数据
    onRefresh: function() {
      this.triggerEvent('refresh', {
        type: this.data.type
      });
    },

    // 设置预警
    onSetAlert: function() {
      this.triggerEvent('setAlert', {
        type: this.data.type,
        threshold: this.data.threshold
      });
    },

    // 格式化更新时间
    formatUpdateTime: function(timeStr) {
      if (!timeStr) return '';
      
      const time = new Date(timeStr);
      const now = new Date();
      const diff = Math.floor((now - time) / 1000);

      if (diff < 60) return '刚刚更新';
      if (diff < 3600) return `${Math.floor(diff / 60)}分钟前`;
      if (diff < 86400) return `${Math.floor(diff / 3600)}小时前`;
      return time.toLocaleDateString();
    }
  },

  lifetimes: {
    attached: function() {
      // 组件初始化
      const config = this.data.typeConfig[this.data.type] || this.data.typeConfig.temperature;
      const statusConfig = this.data.statusConfig[this.data.status] || this.data.statusConfig.normal;
      
      this.setData({
        currentConfig: config,
        currentStatusConfig: statusConfig
      });

      // 初始化显示值
      if (this.data.value !== undefined) {
        this.updateDisplayValue(this.data.value);
        this.checkThreshold(this.data.value);
      }

      // 计算趋势
      this.calculateTrend();
    },

    ready: function() {
      // 组件布局完成后执行
      this.calculateTrend();
    }
  },

  observers: {
    // 监听状态变化
    'status': function(status) {
      const statusConfig = this.data.statusConfig[status] || this.data.statusConfig.normal;
      this.setData({
        currentStatusConfig: statusConfig
      });
    },

    // 监听趋势数据变化
    'trend': function(trend) {
      this.calculateTrend();
    }
  }
});